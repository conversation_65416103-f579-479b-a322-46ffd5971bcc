# 🧠 TabTutor - CS Learning on Every New Tab

> Master computer science fundamentals with bite-sized lessons every time you open a new tab.

## 🚀 Features

- **100+ Curated Questions**: Covering Big-O notation, data structures, algorithms, and programming concepts
- **Smart Filtering**: Filter by difficulty (Easy/Medium/Hard) and category
- **Daily Mode**: Get one consistent question per day for focused learning
- **Flip Cards**: Interactive question/answer format
- **Keyboard Shortcuts**: Space to flip, N for next question
- **Beautiful UI**: Clean, modern design that's easy on the eyes

## 📦 Installation

### For Development/Testing

1. **Clone or Download** this repository to your local machine
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer Mode** (toggle in the top right)
4. **Click "Load unpacked"** and select the TabTutor folder
5. **Open a new tab** to start learning!

### From Chrome Web Store (Coming Soon)

The extension will be available on the Chrome Web Store soon!

## 🎯 How to Use

1. **Open a new tab** - TabTutor automatically replaces your new tab page
2. **Read the question** - Each question is carefully curated to teach CS fundamentals
3. **Think about it** - Take a moment to consider your answer
4. **Flip to see the answer** - Click "Show Answer" or press Space/Enter
5. **Learn something new** - Read the explanation and internalize the concept
6. **Get another question** - Click "Next Question" or press N

## ⌨️ Keyboard Shortcuts

- **Space/Enter**: Flip the card to show/hide answer
- **N or →**: Next question
- **P or ←**: Previous question (currently shows new random question)

## ⚙️ Settings

Access settings through:
- The settings button on the new tab page
- The extension popup (click the TabTutor icon in your toolbar)

### Available Settings:
- **Difficulty Filter**: Choose Easy, Medium, Hard, or All
- **Category Filter**: Focus on specific topics like Big-O, Data Structures, etc.
- **Daily Mode**: Get the same question all day for deeper reflection

## 📚 Question Categories

- **Big-O Notation**: Time and space complexity analysis
- **Data Structures**: Arrays, trees, graphs, heaps, and more
- **Algorithms**: Sorting, searching, dynamic programming, etc.
- **Programming**: Language features, paradigms, and best practices

## 🛠️ Technical Details

- **Manifest V3**: Built with the latest Chrome extension standards
- **Local Storage**: Settings are synced across your Chrome instances
- **No External Dependencies**: Everything runs locally for privacy and speed
- **Responsive Design**: Works on all screen sizes

## 🎨 Customization

The extension is designed to be easily customizable:
- Add your own questions to `data/questions.json`
- Modify the CSS in `css/newtab.css` for different themes
- Extend functionality in `js/newtab.js`

## 📝 Question Format

Questions follow this JSON structure:
```json
{
  "id": 1,
  "category": "Big-O",
  "question": "What's the time complexity of binary search?",
  "answer": "O(log n) - We eliminate half the search space with each comparison.",
  "difficulty": "easy"
}
```

## 🤝 Contributing

Want to add more questions or improve the extension?
1. Fork the repository
2. Add your questions to `data/questions.json`
3. Test your changes
4. Submit a pull request

## 📄 License

MIT License - feel free to use, modify, and distribute!

## 🔮 Future Features

- [ ] Progress tracking and statistics
- [ ] Spaced repetition algorithm
- [ ] Custom question sets
- [ ] Dark mode theme
- [ ] Export/import question sets
- [ ] Achievement system

---

**Happy Learning!** 🎓

Made with ❤️ for the developer community.
