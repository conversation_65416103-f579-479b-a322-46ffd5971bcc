/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 800px;
    padding: 2rem;
    text-align: center;
}

/* Header */
.header {
    margin-bottom: 3rem;
}

.logo {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.logo-icon {
    font-size: 2.5rem;
}

.tagline {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 300;
}

/* Question Card */
.question-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    margin-bottom: 2rem;
    position: relative;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.question-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.question-side,
.answer-side {
    width: 100%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: translateX(0);
}

.answer-side {
    display: none;
    opacity: 0;
    transform: translateX(20px);
}

.question-card.flipped .question-side {
    display: none;
    opacity: 0;
    transform: translateX(-20px);
}

.question-card.flipped .answer-side {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

/* Category and Difficulty */
.category-badge {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.difficulty-indicator {
    display: flex;
    justify-content: center;
    gap: 0.3rem;
    margin-bottom: 1.5rem;
}

.difficulty-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ddd;
    transition: background 0.3s ease;
}

.difficulty-indicator.easy .difficulty-dot:nth-child(1) {
    background: #4CAF50;
}

.difficulty-indicator.medium .difficulty-dot:nth-child(1),
.difficulty-indicator.medium .difficulty-dot:nth-child(2) {
    background: #FF9800;
}

.difficulty-indicator.hard .difficulty-dot {
    background: #F44336;
}

/* Question and Answer Text */
.question-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    line-height: 1.4;
}

.answer-label {
    font-size: 1.2rem;
    color: #667eea;
    margin-bottom: 1rem;
    font-weight: 600;
}

.answer-text {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 2rem;
    line-height: 1.5;
}

/* Buttons */
.flip-button,
.control-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.flip-button:hover,
.control-button:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.flip-button.secondary,
.control-button.secondary {
    background: #6c757d;
}

.flip-button.secondary:hover,
.control-button.secondary:hover {
    background: #5a6268;
}

.flip-icon,
.control-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.flip-button:hover .flip-icon,
.control-button:hover .control-icon {
    transform: scale(1.1);
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Footer */
.footer {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.stats {
    margin-bottom: 1rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.separator {
    margin: 0 0.5rem;
    opacity: 0.5;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    font-size: 1.5rem;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #333;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
}

.setting-group select:focus {
    outline: none;
    border-color: #667eea;
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .question-card {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .question-text {
        font-size: 1.3rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .logo {
        font-size: 2.5rem;
    }
}
