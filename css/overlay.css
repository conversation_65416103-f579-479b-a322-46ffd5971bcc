/* NerdAlert Overlay Styles - Programmer Theme */

#nerdalert-overlay {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 2147483647; /* Maximum z-index */
    padding: 20px;
    pointer-events: none;
    font-family: '<PERSON>', '<PERSON><PERSON>', 'Ubuntu Mono', '<PERSON>sol<PERSON>', 'source-code-pro', monospace;
    font-size: 14px;
    line-height: 1.4;
    
    /* Animation */
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#nerdalert-overlay.nerdalert-visible {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

.nerdalert-card {
    background: #1a1a1a;
    border: 2px solid #333;
    border-radius: 8px;
    width: 380px;
    max-width: 90vw;
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    
    /* Terminal glow effect */
    position: relative;
}

.nerdalert-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ff41, #0066cc, #ff6b35, #00ff41);
    border-radius: 10px;
    z-index: -1;
    opacity: 0.3;
    animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Header */
.nerdalert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    border-bottom: 1px solid #333;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-radius: 6px 6px 0 0;
}

.nerdalert-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #00ff41;
    font-weight: bold;
    font-size: 16px;
}

.nerdalert-icon {
    font-size: 18px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.nerdalert-title {
    color: #ffffff;
    text-shadow: 0 0 10px #00ff41;
}

.nerdalert-close {
    background: none;
    border: none;
    color: #888;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.nerdalert-close:hover {
    background: #ff4444;
    color: white;
    transform: scale(1.1);
}

/* Content */
.nerdalert-content {
    padding: 20px;
    background: #1a1a1a;
    color: #e0e0e0;
}

.nerdalert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.nerdalert-category {
    background: linear-gradient(135deg, #0066cc, #004499);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid #0088ff;
    box-shadow: 0 0 10px rgba(0, 136, 255, 0.3);
}

.nerdalert-difficulty {
    color: #00ff41;
    font-size: 16px;
    letter-spacing: 2px;
}

.nerdalert-question {
    color: #ffffff;
    font-size: 16px;
    font-weight: normal;
    margin: 0 0 20px 0;
    line-height: 1.5;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.nerdalert-answer-label {
    color: #00ff41;
    font-weight: bold;
    margin-bottom: 12px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nerdalert-answer {
    color: #e0e0e0;
    margin: 0 0 20px 0;
    line-height: 1.6;
    background: rgba(0, 255, 65, 0.05);
    padding: 16px;
    border-radius: 6px;
    border-left: 3px solid #00ff41;
}

/* Buttons */
.nerdalert-btn {
    background: linear-gradient(135deg, #333, #222);
    border: 1px solid #555;
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nerdalert-btn:hover {
    background: linear-gradient(135deg, #444, #333);
    border-color: #00ff41;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
    transform: translateY(-1px);
}

.nerdalert-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nerdalert-btn.secondary {
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border-color: #444;
    color: #ccc;
}

.nerdalert-btn.secondary:hover {
    border-color: #0066cc;
    box-shadow: 0 0 15px rgba(0, 102, 204, 0.3);
}

.nerdalert-btn.small {
    padding: 8px 16px;
    font-size: 12px;
}

.nerdalert-arrow {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.nerdalert-btn:hover .nerdalert-arrow {
    transform: translateX(2px);
}

.nerdalert-btn.secondary:hover .nerdalert-arrow {
    transform: translateX(-2px);
}

/* Footer */
.nerdalert-footer {
    padding: 16px 20px;
    border-top: 1px solid #333;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-radius: 0 0 6px 6px;
    display: flex;
    gap: 12px;
    justify-content: space-between;
}

/* Responsive */
@media (max-width: 480px) {
    #nerdalert-overlay {
        padding: 10px;
    }
    
    .nerdalert-card {
        width: 100%;
        max-width: calc(100vw - 20px);
    }
    
    .nerdalert-question {
        font-size: 14px;
    }
    
    .nerdalert-footer {
        flex-direction: column;
        gap: 8px;
    }
}

/* Terminal cursor effect */
.nerdalert-question::after {
    content: '▋';
    color: #00ff41;
    animation: blink 1s infinite;
    margin-left: 2px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Syntax highlighting for code in answers */
.nerdalert-answer code {
    background: #2a2a2a;
    color: #ff6b35;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: inherit;
    border: 1px solid #444;
}

/* Scrollbar styling for long content */
.nerdalert-content::-webkit-scrollbar {
    width: 6px;
}

.nerdalert-content::-webkit-scrollbar-track {
    background: #2a2a2a;
}

.nerdalert-content::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
}

.nerdalert-content::-webkit-scrollbar-thumb:hover {
    background: #777;
}
