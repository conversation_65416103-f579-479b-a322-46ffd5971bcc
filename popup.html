<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabTutor Settings</title>
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .version {
            font-size: 0.8rem;
            color: #666;
        }
        
        .setting-group {
            margin-bottom: 15px;
        }
        
        .setting-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .setting-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .stats h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #333;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🧠 TabTutor</div>
        <div class="version">v1.0.0</div>
    </div>

    <div class="setting-group">
        <label for="difficultyFilter">Difficulty Level:</label>
        <select id="difficultyFilter">
            <option value="all">All Levels</option>
            <option value="easy">Easy</option>
            <option value="medium">Medium</option>
            <option value="hard">Hard</option>
        </select>
    </div>

    <div class="setting-group">
        <label for="categoryFilter">Category:</label>
        <select id="categoryFilter">
            <option value="all">All Categories</option>
            <option value="Big-O">Big-O Notation</option>
            <option value="Data Structures">Data Structures</option>
            <option value="Algorithms">Algorithms</option>
            <option value="Programming">Programming</option>
        </select>
    </div>

    <div class="setting-group">
        <div class="checkbox-group">
            <input type="checkbox" id="dailyMode">
            <label for="dailyMode">Daily Mode (one question per day)</label>
        </div>
    </div>

    <div class="stats">
        <h3>Quick Stats</h3>
        <div class="stat-item">
            <span>Total Questions:</span>
            <span id="totalQuestions">100</span>
        </div>
        <div class="stat-item">
            <span>Current Filter:</span>
            <span id="currentFilter">All</span>
        </div>
        <div class="stat-item">
            <span>Mode:</span>
            <span id="currentMode">Random</span>
        </div>
    </div>

    <div class="links">
        <a href="#" id="newTabLink">Open New Tab</a>
        <a href="#" id="feedbackLink">Feedback</a>
    </div>

    <script src="js/popup.js"></script>
</body>
</html>
