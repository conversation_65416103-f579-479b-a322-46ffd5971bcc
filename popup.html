<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NerdAlert Settings</title>
    <style>
        body {
            width: 320px;
            padding: 0;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', 'Consolas', monospace;
            margin: 0;
            background: #1a1a1a;
            color: #e0e0e0;
            font-size: 13px;
        }

        .header {
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
            padding: 20px;
            border-bottom: 2px solid #333;
            text-align: center;
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #00ff41;
            text-shadow: 0 0 10px #00ff41;
        }

        .tagline {
            font-size: 11px;
            opacity: 0.7;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content {
            padding: 20px;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 12px;
            color: #00ff41;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            font-size: 12px;
            background: #2a2a2a;
            color: #e0e0e0;
            font-family: inherit;
        }

        select:focus {
            outline: none;
            border-color: #00ff41;
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 0;
        }

        input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .interval-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .interval-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #2a2a2a;
            color: #e0e0e0;
            font-family: inherit;
            font-size: 12px;
        }

        .interval-unit {
            color: #888;
            font-size: 11px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #555;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #00ff41;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .test-button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #333, #222);
            border: 1px solid #555;
            color: #ffffff;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: linear-gradient(135deg, #444, #333);
            border-color: #00ff41;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
        }

        .stats {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid #333;
            padding: 15px;
            border-radius: 4px;
            font-size: 11px;
            margin-bottom: 15px;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .stats-value {
            color: #00ff41;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #333;
            background: #2a2a2a;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🚨 NerdAlert</div>
        <div class="tagline">Configuration Panel</div>
    </div>

    <div class="content">
        <div class="setting-group">
            <label>Extension Status:</label>
            <div class="checkbox-label">
                <label class="toggle-switch">
                    <input type="checkbox" id="enabledToggle">
                    <span class="slider"></span>
                </label>
                <span style="margin-left: 10px;">Enable Alerts</span>
            </div>
        </div>

        <div class="setting-group">
            <label for="intervalSetting">Alert Interval:</label>
            <div class="interval-group">
                <input type="number" id="intervalSetting" class="interval-input" min="5" max="120" value="30">
                <span class="interval-unit">minutes</span>
            </div>
        </div>

        <div class="setting-group">
            <label for="difficultyFilter">Difficulty Level:</label>
            <select id="difficultyFilter">
                <option value="all">All Levels</option>
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
            </select>
        </div>

        <div class="setting-group">
            <label for="categoryFilter">Category:</label>
            <select id="categoryFilter">
                <option value="all">All Categories</option>
                <option value="Big-O">Big-O Notation</option>
                <option value="Data Structures">Data Structures</option>
                <option value="Algorithms">Algorithms</option>
                <option value="Programming">Programming</option>
            </select>
        </div>

        <button class="test-button" id="testAlert">Test Alert Now</button>

        <div class="stats" id="stats">
            <div class="stats-row">
                <span>Questions Available:</span>
                <span class="stats-value" id="questionCount">Loading...</span>
            </div>
            <div class="stats-row">
                <span>Next Alert:</span>
                <span class="stats-value" id="nextAlert">Loading...</span>
            </div>
            <div class="stats-row">
                <span>Current Filters:</span>
                <span class="stats-value" id="currentFilters">Loading...</span>
            </div>
        </div>
    </div>

    <div class="footer">
        NerdAlert v1.0 • Made for developers
    </div>

    <script src="js/popup.js"></script>
</body>
</html>
