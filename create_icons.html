<!DOCTYPE html>
<html>
<head>
    <title>TabTutor Icon Generator</title>
</head>
<body>
    <h1>TabTutor Icons</h1>
    
    <!-- 16x16 Icon -->
    <h3>16x16</h3>
    <canvas id="icon16" width="16" height="16"></canvas>
    
    <!-- 32x32 Icon -->
    <h3>32x32</h3>
    <canvas id="icon32" width="32" height="32"></canvas>
    
    <!-- 48x48 Icon -->
    <h3>48x48</h3>
    <canvas id="icon48" width="48" height="48"></canvas>
    
    <!-- 128x128 Icon -->
    <h3>128x128</h3>
    <canvas id="icon128" width="128" height="128"></canvas>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw brain emoji or simple brain shape
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (size >= 32) {
                ctx.fillText('🧠', size/2, size/2);
            } else {
                // For small sizes, draw a simple shape
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/4, 0, 2 * Math.PI);
                ctx.fill();
                
                // Add some brain-like curves
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(size/2 - size/8, size/2, size/8, 0, Math.PI);
                ctx.arc(size/2 + size/8, size/2, size/8, 0, Math.PI);
                ctx.stroke();
            }
        }
        
        // Generate all icons
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon32'), 32);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
        
        // Function to download canvas as PNG
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Add download buttons
        ['16', '32', '48', '128'].forEach(size => {
            const button = document.createElement('button');
            button.textContent = `Download ${size}x${size}`;
            button.onclick = () => downloadCanvas(document.getElementById(`icon${size}`), `icon${size}.png`);
            document.body.appendChild(button);
            document.body.appendChild(document.createElement('br'));
        });
    </script>
</body>
</html>
