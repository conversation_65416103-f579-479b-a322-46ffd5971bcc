# 📦 TabTutor Installation Guide

## Quick Start (5 minutes)

### Step 1: Download the Extension
- Download or clone this repository to your computer
- Make sure all files are in a single folder named `TabTutor` or similar

### Step 2: Open Chrome Extensions
1. Open Google Chrome
2. Type `chrome://extensions/` in the address bar and press Enter
3. Or go to Chrome menu → More tools → Extensions

### Step 3: Enable Developer Mode
1. In the top-right corner of the Extensions page, toggle **"Developer mode"** ON
2. You should see new buttons appear: "Load unpacked", "Pack extension", "Update"

### Step 4: Load the Extension
1. Click **"Load unpacked"**
2. Navigate to and select the TabTutor folder (the one containing `manifest.json`)
3. Click **"Select Folder"** or **"Open"**

### Step 5: Test It!
1. Open a new tab (Ctrl+T or Cmd+T)
2. You should see TabTutor instead of the default new tab page
3. Try clicking "Show Answer" and "Next Question"

## 🎉 Success!

If you see a CS question with the TabTutor interface, you're all set! 

## 🔧 Troubleshooting

### Extension Not Loading?
- **Check the folder**: Make sure you selected the folder containing `manifest.json`
- **Check file structure**: Ensure all files are in the correct locations:
  ```
  TabTutor/
  ├── manifest.json
  ├── newtab.html
  ├── popup.html
  ├── css/newtab.css
  ├── js/newtab.js
  ├── js/popup.js
  ├── data/questions.json
  └── icons/
  ```

### Questions Not Loading?
- **Check browser console**: Press F12 on the new tab page and look for errors
- **File permissions**: Make sure Chrome can read all files in the folder
- **JSON syntax**: The questions.json file must be valid JSON

### New Tab Not Changing?
- **Refresh**: Try closing and reopening Chrome
- **Check conflicts**: Disable other new tab extensions temporarily
- **Reload extension**: Go to chrome://extensions/ and click the reload button on TabTutor

### Icons Not Showing?
- **SVG support**: Make sure your Chrome version supports SVG icons
- **File paths**: Check that icon files exist in the `icons/` folder

## 🚀 Advanced Setup

### Custom Questions
1. Edit `data/questions.json` to add your own questions
2. Follow the existing format:
   ```json
   {
     "id": 101,
     "category": "Your Category",
     "question": "Your question here?",
     "answer": "Your detailed answer here.",
     "difficulty": "easy"
   }
   ```
3. Reload the extension to see changes

### Styling Changes
1. Edit `css/newtab.css` to customize the appearance
2. Change colors, fonts, layout, etc.
3. Reload the extension to see changes

### Adding Features
1. Modify `js/newtab.js` for new functionality
2. Update `manifest.json` if you need new permissions
3. Test thoroughly before using

## 📱 Browser Compatibility

- **Chrome**: Full support (recommended)
- **Edge**: Should work (Chromium-based)
- **Brave**: Should work (Chromium-based)
- **Firefox**: Not compatible (different extension format)
- **Safari**: Not compatible (different extension format)

## 🔒 Privacy & Security

- **Local only**: All data stays on your computer
- **No tracking**: No analytics or data collection
- **No network**: Extension works completely offline
- **Open source**: You can inspect all code

## 🆘 Getting Help

If you're still having issues:

1. **Check the test page**: Open `test.html` in your browser to see if the basic functionality works
2. **Browser console**: Press F12 and check for error messages
3. **File permissions**: Make sure Chrome can read the extension folder
4. **Chrome version**: Ensure you're using a recent version of Chrome

## 🎯 Next Steps

Once installed:
- Explore the settings (click the gear icon)
- Try different difficulty levels and categories
- Use keyboard shortcuts (Space to flip, N for next)
- Set up daily mode for consistent learning

Happy learning! 🧠✨
