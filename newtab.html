<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabTutor - Learn CS Fundamentals</title>
    <link rel="stylesheet" href="css/newtab.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="logo">
                <span class="logo-icon">🧠</span>
                TabTutor
            </h1>
            <p class="tagline">Master CS fundamentals, one tab at a time</p>
        </header>

        <main class="main-content">
            <div class="question-card" id="questionCard">
                <div class="question-side" id="questionSide">
                    <div class="category-badge" id="categoryBadge">Loading...</div>
                    <div class="difficulty-indicator" id="difficultyIndicator">
                        <span class="difficulty-dot"></span>
                        <span class="difficulty-dot"></span>
                        <span class="difficulty-dot"></span>
                    </div>
                    <h2 class="question-text" id="questionText">Loading your CS nugget...</h2>
                    <button class="flip-button" id="flipButton">
                        <span>Show Answer</span>
                        <span class="flip-icon">→</span>
                    </button>
                </div>

                <div class="answer-side" id="answerSide">
                    <div class="category-badge" id="answerCategoryBadge">Answer</div>
                    <h3 class="answer-label">Answer:</h3>
                    <p class="answer-text" id="answerText">Loading answer...</p>
                    <button class="flip-button secondary" id="backButton">
                        <span class="flip-icon">←</span>
                        <span>Back to Question</span>
                    </button>
                </div>
            </div>

            <div class="controls">
                <button class="control-button" id="nextButton">
                    <span>Next Question</span>
                    <span class="control-icon">⟳</span>
                </button>
                <button class="control-button secondary" id="settingsButton">
                    <span class="control-icon">⚙</span>
                    <span>Settings</span>
                </button>
            </div>
        </main>

        <footer class="footer">
            <div class="stats" id="stats">
                <span>Question <span id="currentQuestion">1</span> of <span id="totalQuestions">100</span></span>
                <span class="separator">•</span>
                <span id="dailyProgress">Today's learning</span>
            </div>
            <div class="footer-links">
                <a href="#" id="aboutLink">About TabTutor</a>
                <span class="separator">•</span>
                <a href="#" id="feedbackLink">Feedback</a>
            </div>
        </footer>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings</h3>
                <button class="close-button" id="closeSettings">×</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="difficultyFilter">Difficulty Level:</label>
                    <select id="difficultyFilter">
                        <option value="all">All Levels</option>
                        <option value="easy">Easy</option>
                        <option value="medium">Medium</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="categoryFilter">Category:</label>
                    <select id="categoryFilter">
                        <option value="all">All Categories</option>
                        <option value="Big-O">Big-O Notation</option>
                        <option value="Data Structures">Data Structures</option>
                        <option value="Algorithms">Algorithms</option>
                        <option value="Programming">Programming</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="dailyMode">
                        Daily Mode (one question per day)
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script src="js/newtab.js"></script>
</body>
</html>
