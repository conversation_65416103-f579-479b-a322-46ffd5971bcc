<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NerdAlert Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 NerdAlert Extension Test Page</h1>


        <div class="instructions">
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Make sure the NerdAlert extension is loaded in Chrome</li>
                <li>Click the NerdAlert icon 🚨 in the toolbar to open settings</li>
                <li>Click "Test Alert Now" button in the popup</li>
                <li>You should see a programming question overlay in the top-right corner</li>
                <li>Check the browser console (F12) for any error messages</li>
            </ol>
        </div>

        <div>
            <button class="test-button" onclick="testConsole()">Test Console Logging</button>
        </div>

        <div style="margin-top: 30px;">
            <h3>Expected Behavior:</h3>
            <ul>
                <li>✅ Extension icon appears in Chrome toolbar</li>
                <li>✅ Clicking icon opens NerdAlert settings popup</li>
                <li>✅ Settings popup has dark programmer theme</li>
                <li>✅ "Test Alert Now" button triggers overlay</li>
                <li>✅ Overlay appears in top-right with CS question</li>
                <li>✅ Overlay has terminal/programmer styling</li>
                <li>✅ Can interact with overlay (show answer, close, etc.)</li>
            </ul>
        </div>

    </div>

    <script>
        function testConsole() {
            console.log('NerdAlert Test: Console logging works');
        }

        // Log page load
        console.log('NerdAlert Test Page: Loaded successfully');

        // Listen for any NerdAlert messages
        window.addEventListener('message', (event) => {
            console.log('NerdAlert Test: Received message:', event.data);
        });
    </script>
</body>
</html>
