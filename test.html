<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabTutor Test</title>
    <link rel="stylesheet" href="css/newtab.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="logo">
                <span class="logo-icon">🧠</span>
                TabTutor (Test Mode)
            </h1>
            <p class="tagline">Testing the extension locally</p>
        </header>

        <main class="main-content">
            <div class="question-card" id="questionCard">
                <div class="question-side" id="questionSide">
                    <div class="category-badge" id="categoryBadge">Loading...</div>
                    <div class="difficulty-indicator" id="difficultyIndicator">
                        <span class="difficulty-dot"></span>
                        <span class="difficulty-dot"></span>
                        <span class="difficulty-dot"></span>
                    </div>
                    <h2 class="question-text" id="questionText">Loading your CS nugget...</h2>
                    <button class="flip-button" id="flipButton">
                        <span>Show Answer</span>
                        <span class="flip-icon">→</span>
                    </button>
                </div>

                <div class="answer-side" id="answerSide">
                    <div class="category-badge" id="answerCategoryBadge">Answer</div>
                    <h3 class="answer-label">Answer:</h3>
                    <p class="answer-text" id="answerText">Loading answer...</p>
                    <button class="flip-button secondary" id="backButton">
                        <span class="flip-icon">←</span>
                        <span>Back to Question</span>
                    </button>
                </div>
            </div>

            <div class="controls">
                <button class="control-button" id="nextButton">
                    <span>Next Question</span>
                    <span class="control-icon">⟳</span>
                </button>
                <button class="control-button secondary" id="settingsButton">
                    <span class="control-icon">⚙</span>
                    <span>Settings</span>
                </button>
            </div>
        </main>

        <footer class="footer">
            <div class="stats" id="stats">
                <span>Question <span id="currentQuestion">1</span> of <span id="totalQuestions">100</span></span>
                <span class="separator">•</span>
                <span id="dailyProgress">Test mode</span>
            </div>
            <div class="footer-links">
                <span>Press Space to flip • N for next question</span>
            </div>
        </footer>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings</h3>
                <button class="close-button" id="closeSettings">×</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="difficultyFilter">Difficulty Level:</label>
                    <select id="difficultyFilter">
                        <option value="all">All Levels</option>
                        <option value="easy">Easy</option>
                        <option value="medium">Medium</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="categoryFilter">Category:</label>
                    <select id="categoryFilter">
                        <option value="all">All Categories</option>
                        <option value="Big-O">Big-O Notation</option>
                        <option value="Data Structures">Data Structures</option>
                        <option value="Algorithms">Algorithms</option>
                        <option value="Programming">Programming</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="dailyMode">
                        Daily Mode (one question per day)
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Modified version of newtab.js for testing without Chrome extension APIs
        class TabTutorTest {
            constructor() {
                this.questions = [];
                this.currentQuestion = null;
                this.currentIndex = 0;
                this.settings = {
                    difficulty: 'all',
                    category: 'all',
                    dailyMode: false
                };
                
                this.init();
            }

            async init() {
                await this.loadQuestions();
                this.setupEventListeners();
                this.displayQuestion();
                this.updateStats();
            }

            async loadQuestions() {
                try {
                    const response = await fetch('data/questions.json');
                    const data = await response.json();
                    this.questions = data.questions;
                    console.log('Loaded', this.questions.length, 'questions');
                } catch (error) {
                    console.error('Failed to load questions:', error);
                    this.showError('Failed to load questions. Please refresh the page.');
                }
            }

            setupEventListeners() {
                document.getElementById('flipButton').addEventListener('click', () => this.flipCard());
                document.getElementById('backButton').addEventListener('click', () => this.flipCard(false));
                document.getElementById('nextButton').addEventListener('click', () => this.nextQuestion());
                document.getElementById('settingsButton').addEventListener('click', () => this.showSettings());
                document.getElementById('closeSettings').addEventListener('click', () => this.hideSettings());

                document.getElementById('difficultyFilter').addEventListener('change', (e) => {
                    this.settings.difficulty = e.target.value;
                    this.displayQuestion();
                });

                document.getElementById('categoryFilter').addEventListener('change', (e) => {
                    this.settings.category = e.target.value;
                    this.displayQuestion();
                });

                document.getElementById('dailyMode').addEventListener('change', (e) => {
                    this.settings.dailyMode = e.target.checked;
                    this.displayQuestion();
                });

                document.addEventListener('keydown', (e) => {
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        this.flipCard();
                    } else if (e.key === 'n' || e.key === 'ArrowRight') {
                        e.preventDefault();
                        this.nextQuestion();
                    }
                });
            }

            getFilteredQuestions() {
                return this.questions.filter(q => {
                    const difficultyMatch = this.settings.difficulty === 'all' || q.difficulty === this.settings.difficulty;
                    const categoryMatch = this.settings.category === 'all' || q.category === this.settings.category;
                    return difficultyMatch && categoryMatch;
                });
            }

            displayQuestion() {
                const filteredQuestions = this.getFilteredQuestions();
                if (filteredQuestions.length === 0) {
                    this.showError('No questions match your current filters.');
                    return;
                }

                this.currentIndex = Math.floor(Math.random() * filteredQuestions.length);
                this.currentQuestion = filteredQuestions[this.currentIndex];
                this.renderQuestion();
            }

            renderQuestion() {
                if (!this.currentQuestion) return;

                document.getElementById('categoryBadge').textContent = this.currentQuestion.category;
                document.getElementById('answerCategoryBadge').textContent = this.currentQuestion.category;
                document.getElementById('questionText').textContent = this.currentQuestion.question;
                document.getElementById('answerText').textContent = this.currentQuestion.answer;

                const difficultyIndicator = document.getElementById('difficultyIndicator');
                difficultyIndicator.className = `difficulty-indicator ${this.currentQuestion.difficulty}`;

                document.getElementById('questionCard').classList.remove('flipped');
            }

            flipCard(toAnswer = true) {
                const card = document.getElementById('questionCard');
                if (toAnswer) {
                    card.classList.add('flipped');
                } else {
                    card.classList.remove('flipped');
                }
            }

            nextQuestion() {
                this.displayQuestion();
                this.updateStats();
            }

            updateStats() {
                const filteredQuestions = this.getFilteredQuestions();
                document.getElementById('totalQuestions').textContent = filteredQuestions.length;
            }

            showSettings() {
                document.getElementById('settingsModal').classList.add('show');
            }

            hideSettings() {
                document.getElementById('settingsModal').classList.remove('show');
            }

            showError(message) {
                document.getElementById('questionText').textContent = message;
                document.getElementById('categoryBadge').textContent = 'Error';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new TabTutorTest();
        });
    </script>
</body>
</html>
