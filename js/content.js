// NerdAlert Content Script
// Injects the learning overlay into web pages

class NerdAlert {
    constructor() {
        this.overlay = null;
        this.isVisible = false;
        this.settings = {
            interval: 30, // minutes
            difficulty: 'all',
            category: 'all',
            enabled: true,
            lastShown: 0
        };
        
        this.questions = [];
        this.currentQuestion = null;
        
        this.init();
    }

    async init() {
        // Don't run on extension pages or certain sites
        if (this.shouldSkipSite()) {
            return;
        }

        await this.loadSettings();
        await this.loadQuestions();
        
        if (this.settings.enabled) {
            this.scheduleNextAlert();
            this.setupMessageListener();
        }
    }

    shouldSkipSite() {
        const hostname = window.location.hostname;
        const skipDomains = [
            'chrome-extension://',
            'chrome://',
            'edge://',
            'about:',
            'moz-extension://'
        ];
        
        return skipDomains.some(domain => window.location.href.startsWith(domain));
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['nerdAlertSettings']);
            if (result.nerdAlertSettings) {
                this.settings = { ...this.settings, ...result.nerdAlertSettings };
            }
        } catch (error) {
            console.error('NerdAlert: Failed to load settings:', error);
        }
    }

    async loadQuestions() {
        try {
            const response = await fetch(chrome.runtime.getURL('data/questions.json'));
            const data = await response.json();
            this.questions = data.questions;
        } catch (error) {
            console.error('NerdAlert: Failed to load questions:', error);
        }
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'showAlert') {
                this.showAlert();
                sendResponse({ success: true });
            } else if (request.action === 'updateSettings') {
                this.settings = { ...this.settings, ...request.settings };
                this.scheduleNextAlert();
                sendResponse({ success: true });
            } else if (request.action === 'ping') {
                sendResponse({ success: true });
            }
            return true; // Keep message channel open for async response
        });
    }

    scheduleNextAlert() {
        if (!this.settings.enabled) return;

        const now = Date.now();
        const intervalMs = this.settings.interval * 60 * 1000; // Convert minutes to milliseconds
        const timeSinceLastShown = now - this.settings.lastShown;
        
        let timeUntilNext;
        if (timeSinceLastShown >= intervalMs) {
            // Show immediately if enough time has passed
            timeUntilNext = 1000; // 1 second delay
        } else {
            // Schedule for the remaining time
            timeUntilNext = intervalMs - timeSinceLastShown;
        }

        setTimeout(() => {
            this.showAlert();
        }, timeUntilNext);
    }

    getFilteredQuestions() {
        return this.questions.filter(q => {
            const difficultyMatch = this.settings.difficulty === 'all' || q.difficulty === this.settings.difficulty;
            const categoryMatch = this.settings.category === 'all' || q.category === this.settings.category;
            return difficultyMatch && categoryMatch;
        });
    }

    selectRandomQuestion() {
        const filteredQuestions = this.getFilteredQuestions();
        if (filteredQuestions.length === 0) return null;
        
        const randomIndex = Math.floor(Math.random() * filteredQuestions.length);
        return filteredQuestions[randomIndex];
    }

    showAlert() {
        console.log('NerdAlert: showAlert called, isVisible:', this.isVisible, 'enabled:', this.settings.enabled);

        if (this.isVisible || !this.settings.enabled) return;

        this.currentQuestion = this.selectRandomQuestion();
        if (!this.currentQuestion) {
            console.log('NerdAlert: No questions available');
            return;
        }

        console.log('NerdAlert: Showing alert with question:', this.currentQuestion.question);
        this.createOverlay();
        this.isVisible = true;

        // Update last shown time
        this.settings.lastShown = Date.now();
        chrome.storage.sync.set({ nerdAlertSettings: this.settings });

        // Schedule next alert
        this.scheduleNextAlert();
    }

    createOverlay() {
        // Remove existing overlay if any
        this.removeOverlay();

        // Create overlay container
        this.overlay = document.createElement('div');
        this.overlay.id = 'nerdalert-overlay';
        this.overlay.innerHTML = this.getOverlayHTML();
        
        // Add to page
        document.body.appendChild(this.overlay);
        
        // Setup event listeners
        this.setupOverlayEvents();
        
        // Animate in
        setTimeout(() => {
            this.overlay.classList.add('nerdalert-visible');
        }, 100);
        
        // Auto-hide after 30 seconds
        setTimeout(() => {
            this.hideAlert();
        }, 30000);
    }

    getOverlayHTML() {
        const question = this.currentQuestion;
        const difficultyDots = this.getDifficultyDots(question.difficulty);
        
        return `
            <div class="nerdalert-card">
                <div class="nerdalert-header">
                    <div class="nerdalert-logo">
                        <span class="nerdalert-icon">🚨</span>
                        <span class="nerdalert-title">NerdAlert</span>
                    </div>
                    <button class="nerdalert-close" id="nerdalert-close">×</button>
                </div>
                
                <div class="nerdalert-content">
                    <div class="nerdalert-meta">
                        <span class="nerdalert-category">${question.category}</span>
                        <div class="nerdalert-difficulty">${difficultyDots}</div>
                    </div>
                    
                    <div class="nerdalert-question-side" id="nerdalert-question-side">
                        <h3 class="nerdalert-question">${question.question}</h3>
                        <button class="nerdalert-btn" id="nerdalert-show-answer">
                            <span>Show Answer</span>
                            <span class="nerdalert-arrow">→</span>
                        </button>
                    </div>
                    
                    <div class="nerdalert-answer-side" id="nerdalert-answer-side" style="display: none;">
                        <div class="nerdalert-answer-label">Answer:</div>
                        <p class="nerdalert-answer">${question.answer}</p>
                        <button class="nerdalert-btn secondary" id="nerdalert-back">
                            <span class="nerdalert-arrow">←</span>
                            <span>Back</span>
                        </button>
                    </div>
                </div>
                
                <div class="nerdalert-footer">
                    <button class="nerdalert-btn small" id="nerdalert-next">Next Question</button>
                    <button class="nerdalert-btn small secondary" id="nerdalert-settings">Settings</button>
                </div>
            </div>
        `;
    }

    getDifficultyDots(difficulty) {
        const dots = ['○', '○', '○'];
        const filled = difficulty === 'easy' ? 1 : difficulty === 'medium' ? 2 : 3;
        
        for (let i = 0; i < filled; i++) {
            dots[i] = '●';
        }
        
        return dots.join(' ');
    }

    setupOverlayEvents() {
        // Close button
        document.getElementById('nerdalert-close').addEventListener('click', () => {
            this.hideAlert();
        });

        // Show answer
        document.getElementById('nerdalert-show-answer').addEventListener('click', () => {
            this.flipCard(true);
        });

        // Back to question
        document.getElementById('nerdalert-back').addEventListener('click', () => {
            this.flipCard(false);
        });

        // Next question
        document.getElementById('nerdalert-next').addEventListener('click', () => {
            this.nextQuestion();
        });

        // Settings
        document.getElementById('nerdalert-settings').addEventListener('click', () => {
            chrome.runtime.sendMessage({ action: 'openSettings' });
        });

        // Click outside to close
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.hideAlert();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeydown.bind(this));
    }

    handleKeydown(e) {
        if (!this.isVisible) return;

        if (e.key === 'Escape') {
            this.hideAlert();
        } else if (e.key === ' ' || e.key === 'Enter') {
            e.preventDefault();
            this.flipCard();
        }
    }

    flipCard(showAnswer = null) {
        const questionSide = document.getElementById('nerdalert-question-side');
        const answerSide = document.getElementById('nerdalert-answer-side');
        
        if (showAnswer === null) {
            showAnswer = questionSide.style.display !== 'none';
        }
        
        if (showAnswer) {
            questionSide.style.display = 'none';
            answerSide.style.display = 'block';
        } else {
            questionSide.style.display = 'block';
            answerSide.style.display = 'none';
        }
    }

    nextQuestion() {
        this.currentQuestion = this.selectRandomQuestion();
        if (this.currentQuestion) {
            // Update content
            const card = this.overlay.querySelector('.nerdalert-card');
            card.innerHTML = this.getOverlayHTML().match(/<div class="nerdalert-card">(.*)<\/div>$/s)[1];
            this.setupOverlayEvents();
        }
    }

    hideAlert() {
        if (!this.isVisible) return;

        this.overlay.classList.remove('nerdalert-visible');
        setTimeout(() => {
            this.removeOverlay();
        }, 300);
        
        this.isVisible = false;
        document.removeEventListener('keydown', this.handleKeydown);
    }

    removeOverlay() {
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
        this.overlay = null;
    }
}

// Initialize NerdAlert when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new NerdAlert();
    });
} else {
    new NerdAlert();
}
