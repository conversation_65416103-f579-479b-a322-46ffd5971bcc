class TabTutor {
    constructor() {
        this.questions = [];
        this.currentQuestion = null;
        this.currentIndex = 0;
        this.settings = {
            difficulty: 'all',
            category: 'all',
            dailyMode: false
        };
        
        this.init();
    }

    async init() {
        await this.loadQuestions();
        await this.loadSettings();
        this.setupEventListeners();
        this.displayQuestion();
        this.updateStats();
    }

    async loadQuestions() {
        try {
            const response = await fetch('data/questions.json');
            const data = await response.json();
            this.questions = data.questions;
        } catch (error) {
            console.error('Failed to load questions:', error);
            this.showError('Failed to load questions. Please refresh the page.');
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['tabTutorSettings']);
            if (result.tabTutorSettings) {
                this.settings = { ...this.settings, ...result.tabTutorSettings };
            }
            this.applySettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({ tabTutorSettings: this.settings });
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    setupEventListeners() {
        // Flip button
        document.getElementById('flipButton').addEventListener('click', () => {
            this.flipCard();
        });

        // Back button
        document.getElementById('backButton').addEventListener('click', () => {
            this.flipCard(false);
        });

        // Next question button
        document.getElementById('nextButton').addEventListener('click', () => {
            this.nextQuestion();
        });

        // Settings button
        document.getElementById('settingsButton').addEventListener('click', () => {
            this.showSettings();
        });

        // Settings modal
        document.getElementById('closeSettings').addEventListener('click', () => {
            this.hideSettings();
        });

        // Settings form
        document.getElementById('difficultyFilter').addEventListener('change', (e) => {
            this.settings.difficulty = e.target.value;
            this.saveSettings();
        });

        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.settings.category = e.target.value;
            this.saveSettings();
        });

        document.getElementById('dailyMode').addEventListener('change', (e) => {
            this.settings.dailyMode = e.target.checked;
            this.saveSettings();
            this.displayQuestion(); // Refresh question based on new mode
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === ' ' || e.key === 'Enter') {
                e.preventDefault();
                this.flipCard();
            } else if (e.key === 'n' || e.key === 'ArrowRight') {
                e.preventDefault();
                this.nextQuestion();
            } else if (e.key === 'p' || e.key === 'ArrowLeft') {
                e.preventDefault();
                this.previousQuestion();
            }
        });

        // Close modal on outside click
        document.getElementById('settingsModal').addEventListener('click', (e) => {
            if (e.target.id === 'settingsModal') {
                this.hideSettings();
            }
        });
    }

    getFilteredQuestions() {
        return this.questions.filter(q => {
            const difficultyMatch = this.settings.difficulty === 'all' || q.difficulty === this.settings.difficulty;
            const categoryMatch = this.settings.category === 'all' || q.category === this.settings.category;
            return difficultyMatch && categoryMatch;
        });
    }

    getDailyQuestionIndex() {
        // Use current date as seed for consistent daily question
        const today = new Date();
        const dateString = today.toISOString().split('T')[0]; // YYYY-MM-DD
        const seed = this.hashCode(dateString);
        const filteredQuestions = this.getFilteredQuestions();
        return Math.abs(seed) % filteredQuestions.length;
    }

    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }

    displayQuestion() {
        const filteredQuestions = this.getFilteredQuestions();
        
        if (filteredQuestions.length === 0) {
            this.showError('No questions match your current filters.');
            return;
        }

        if (this.settings.dailyMode) {
            this.currentIndex = this.getDailyQuestionIndex();
        } else {
            // Random question
            this.currentIndex = Math.floor(Math.random() * filteredQuestions.length);
        }

        this.currentQuestion = filteredQuestions[this.currentIndex];
        this.renderQuestion();
    }

    renderQuestion() {
        if (!this.currentQuestion) return;

        // Update question content
        document.getElementById('categoryBadge').textContent = this.currentQuestion.category;
        document.getElementById('answerCategoryBadge').textContent = this.currentQuestion.category;
        document.getElementById('questionText').textContent = this.currentQuestion.question;
        document.getElementById('answerText').textContent = this.currentQuestion.answer;

        // Update difficulty indicator
        const difficultyIndicator = document.getElementById('difficultyIndicator');
        difficultyIndicator.className = `difficulty-indicator ${this.currentQuestion.difficulty}`;

        // Reset card to question side
        document.getElementById('questionCard').classList.remove('flipped');
    }

    flipCard(toAnswer = true) {
        const card = document.getElementById('questionCard');
        if (toAnswer) {
            card.classList.add('flipped');
        } else {
            card.classList.remove('flipped');
        }
    }

    nextQuestion() {
        this.displayQuestion();
        this.updateStats();
    }

    previousQuestion() {
        // For now, just show a new random question
        this.nextQuestion();
    }

    updateStats() {
        const filteredQuestions = this.getFilteredQuestions();
        document.getElementById('totalQuestions').textContent = filteredQuestions.length;
        
        if (this.settings.dailyMode) {
            document.getElementById('dailyProgress').textContent = "Today's question";
        } else {
            document.getElementById('dailyProgress').textContent = "Random learning";
        }
    }

    showSettings() {
        document.getElementById('settingsModal').classList.add('show');
    }

    hideSettings() {
        document.getElementById('settingsModal').classList.remove('show');
    }

    applySettings() {
        document.getElementById('difficultyFilter').value = this.settings.difficulty;
        document.getElementById('categoryFilter').value = this.settings.category;
        document.getElementById('dailyMode').checked = this.settings.dailyMode;
    }

    showError(message) {
        document.getElementById('questionText').textContent = message;
        document.getElementById('categoryBadge').textContent = 'Error';
        document.getElementById('difficultyIndicator').className = 'difficulty-indicator';
    }
}

// Initialize TabTutor when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TabTutor();
});

// Add some helpful keyboard shortcuts info
document.addEventListener('DOMContentLoaded', () => {
    const helpText = document.createElement('div');
    helpText.style.cssText = `
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
        z-index: 1000;
    `;
    helpText.innerHTML = `
        <strong>Keyboard Shortcuts:</strong><br>
        Space/Enter: Flip card<br>
        N/→: Next question<br>
        P/←: Previous question
    `;
    document.body.appendChild(helpText);

    // Show help on first load
    setTimeout(() => {
        helpText.style.opacity = '1';
        setTimeout(() => {
            helpText.style.opacity = '0';
        }, 3000);
    }, 1000);
});
