class TabTutorPopup {
    constructor() {
        this.settings = {
            difficulty: 'all',
            category: 'all',
            dailyMode: false
        };
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateDisplay();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['tabTutorSettings']);
            if (result.tabTutorSettings) {
                this.settings = { ...this.settings, ...result.tabTutorSettings };
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({ tabTutorSettings: this.settings });
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    setupEventListeners() {
        // Settings controls
        document.getElementById('difficultyFilter').addEventListener('change', async (e) => {
            this.settings.difficulty = e.target.value;
            await this.saveSettings();
            this.updateDisplay();
        });

        document.getElementById('categoryFilter').addEventListener('change', async (e) => {
            this.settings.category = e.target.value;
            await this.saveSettings();
            this.updateDisplay();
        });

        document.getElementById('dailyMode').addEventListener('change', async (e) => {
            this.settings.dailyMode = e.target.checked;
            await this.saveSettings();
            this.updateDisplay();
        });

        // Links
        document.getElementById('newTabLink').addEventListener('click', (e) => {
            e.preventDefault();
            chrome.tabs.create({ url: 'chrome://newtab/' });
            window.close();
        });

        document.getElementById('feedbackLink').addEventListener('click', (e) => {
            e.preventDefault();
            chrome.tabs.create({ url: 'mailto:<EMAIL>?subject=TabTutor Feedback' });
            window.close();
        });
    }

    updateDisplay() {
        // Update form values
        document.getElementById('difficultyFilter').value = this.settings.difficulty;
        document.getElementById('categoryFilter').value = this.settings.category;
        document.getElementById('dailyMode').checked = this.settings.dailyMode;

        // Update stats
        this.updateStats();
    }

    updateStats() {
        // Update current filter display
        let filterText = '';
        if (this.settings.difficulty !== 'all') {
            filterText += this.settings.difficulty.charAt(0).toUpperCase() + this.settings.difficulty.slice(1);
        }
        if (this.settings.category !== 'all') {
            if (filterText) filterText += ', ';
            filterText += this.settings.category;
        }
        if (!filterText) filterText = 'All';
        
        document.getElementById('currentFilter').textContent = filterText;

        // Update mode display
        document.getElementById('currentMode').textContent = this.settings.dailyMode ? 'Daily' : 'Random';

        // Calculate filtered question count (approximate)
        this.calculateQuestionCount();
    }

    async calculateQuestionCount() {
        try {
            const response = await fetch(chrome.runtime.getURL('data/questions.json'));
            const data = await response.json();
            
            const filteredQuestions = data.questions.filter(q => {
                const difficultyMatch = this.settings.difficulty === 'all' || q.difficulty === this.settings.difficulty;
                const categoryMatch = this.settings.category === 'all' || q.category === this.settings.category;
                return difficultyMatch && categoryMatch;
            });

            document.getElementById('totalQuestions').textContent = filteredQuestions.length;
        } catch (error) {
            console.error('Failed to calculate question count:', error);
            document.getElementById('totalQuestions').textContent = '100';
        }
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TabTutorPopup();
});
