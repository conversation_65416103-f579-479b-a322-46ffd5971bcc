// NerdAlert Popup Script
class NerdAlertPopup {
    constructor() {
        this.settings = {
            interval: 30,
            difficulty: 'all',
            category: 'all',
            enabled: true,
            lastShown: 0
        };
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateUI();
        this.loadStats();
        this.updateNextAlertTime();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['nerdAlertSettings']);
            if (result.nerdAlertSettings) {
                this.settings = { ...this.settings, ...result.nerdAlertSettings };
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({ nerdAlertSettings: this.settings });
            // Notify background script about settings change
            chrome.runtime.sendMessage({
                action: 'updateSettings',
                settings: this.settings
            });
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    setupEventListeners() {
        document.getElementById('enabledToggle').addEventListener('change', (e) => {
            this.settings.enabled = e.target.checked;
            this.saveSettings();
            this.updateNextAlertTime();
        });

        document.getElementById('intervalSetting').addEventListener('change', (e) => {
            this.settings.interval = parseInt(e.target.value);
            this.saveSettings();
            this.updateNextAlertTime();
        });

        document.getElementById('difficultyFilter').addEventListener('change', (e) => {
            this.settings.difficulty = e.target.value;
            this.saveSettings();
            this.updateStats();
        });

        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.settings.category = e.target.value;
            this.saveSettings();
            this.updateStats();
        });

        document.getElementById('testAlert').addEventListener('click', () => {
            chrome.runtime.sendMessage({ action: 'testAlert' });
        });
    }

    updateUI() {
        document.getElementById('enabledToggle').checked = this.settings.enabled;
        document.getElementById('intervalSetting').value = this.settings.interval;
        document.getElementById('difficultyFilter').value = this.settings.difficulty;
        document.getElementById('categoryFilter').value = this.settings.category;
    }

    async loadStats() {
        try {
            // Load questions to calculate stats
            const response = await fetch(chrome.runtime.getURL('data/questions.json'));
            const data = await response.json();
            const questions = data.questions;
            
            this.updateStats(questions);
        } catch (error) {
            console.error('Failed to load questions for stats:', error);
        }
    }

    updateStats(questions = null) {
        if (!questions) {
            this.loadStats();
            return;
        }

        const filteredQuestions = questions.filter(q => {
            const difficultyMatch = this.settings.difficulty === 'all' || q.difficulty === this.settings.difficulty;
            const categoryMatch = this.settings.category === 'all' || q.category === this.settings.category;
            return difficultyMatch && categoryMatch;
        });

        document.getElementById('questionCount').textContent = filteredQuestions.length;
        
        // Update filter display
        let filterText = 'All';
        if (this.settings.difficulty !== 'all' || this.settings.category !== 'all') {
            const parts = [];
            if (this.settings.difficulty !== 'all') {
                parts.push(this.settings.difficulty.charAt(0).toUpperCase() + this.settings.difficulty.slice(1));
            }
            if (this.settings.category !== 'all') {
                parts.push(this.settings.category);
            }
            filterText = parts.join(', ');
        }
        document.getElementById('currentFilters').textContent = filterText;
    }

    updateNextAlertTime() {
        if (!this.settings.enabled) {
            document.getElementById('nextAlert').textContent = 'Disabled';
            return;
        }

        const now = Date.now();
        const intervalMs = this.settings.interval * 60 * 1000;
        const timeSinceLastShown = now - this.settings.lastShown;
        
        let nextAlertTime;
        if (timeSinceLastShown >= intervalMs) {
            nextAlertTime = 'Ready now';
        } else {
            const timeUntilNext = intervalMs - timeSinceLastShown;
            const minutesUntilNext = Math.ceil(timeUntilNext / (60 * 1000));
            nextAlertTime = `${minutesUntilNext}m`;
        }
        
        document.getElementById('nextAlert').textContent = nextAlertTime;
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NerdAlertPopup();
});
